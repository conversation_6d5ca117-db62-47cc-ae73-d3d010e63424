# 逆向分析报告

## 目标程序
Windows GUI 程序，核心逻辑位于 `0x140001000`，现已重命名为 `validate_password`。

## 函数概述
`validate_password` 从两个文本框读取字符串：
1. 第一个输入（假设为用户名或明文口令），长度要求 5-10。
2. 第二个输入（加密后口令），长度必须是第一个输入 + 1。

函数算法：
1. 将第一个输入转换为大写。
2. 使用源字符表 `ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789` 查找每个字符索引。
3. 设累计偏移 `off = 0`，对每个字符执行：
   - 计算 `idx' = (idx + off) mod 36`。
   - 在目标字符表 `QWERTYUIOPASDFGHJKLZXCVBNM1234567890` 取位置 `idx'` 的字符作为输出。
   - `off += 3`。
4. 遍历完毕后，再次把每个原字符索引求和，取 `sum mod 10` 作为末尾校验数字，追加到输出串末尾。
5. 若生成串与第二个输入完全一致则验证成功。

## 变量/函数重命名
- 将函数 `sub_140001000` 重命名为 `validate_password`。
- 在函数起始地址添加了详细中文注释，说明算法步骤。

## admin 对应 flag 计算
输入明文：`admin`
转换为大写：`ADMIN`

| 位置 | 字符 | idx(源) | off | idx' | 输出字符 |
|-----|------|---------|-----|------|---------|
| 0 | A | 0  | 0 | 0  | Q |
| 1 | D | 3  | 3 | 6  | U |
| 2 | M | 12 | 6 | 18 | L |
| 3 | I | 8  | 9 | 17 | K |
| 4 | N | 13 | 12| 25 | M |

索引总和 = 0+3+12+8+13 = 36 → `36 mod 10 = 6`。

最终编码串（flag）：`QULKM6`

## 结论
- 当第一个输入为 `admin`（不区分大小写），第二个输入应为 **`QULKM6`** 才能通过验证。

