#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Shellcode Encoder - XOR加密工具
用于加密shellcode.bin文件，防止静态检测
"""

import os
import sys
import struct
import random

def xor_encrypt(data, key):
    """
    使用XOR算法加密数据
    Args:
        data: 要加密的字节数据
        key: 加密密钥（字节）
    Returns:
        加密后的字节数据
    """
    encrypted = bytearray()
    key_len = len(key)
    
    for i, byte in enumerate(data):
        encrypted.append(byte ^ key[i % key_len])
    
    return bytes(encrypted)

def generate_random_key(length=16):
    """
    生成随机密钥
    Args:
        length: 密钥长度
    Returns:
        随机密钥字节
    """
    return bytes([random.randint(1, 255) for _ in range(length)])

def save_encrypted_file(input_file, output_file, keyheader_file):
    """
    加密shellcode文件并保存
    Args:
        input_file: 输入文件路径
        output_file: 输出加密文件路径
        keyheader_file: 密钥头文件路径
    """
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            print(f"[-] 错误: 输入文件 '{input_file}' 不存在")
            return False
        
        # 读取原始shellcode
        with open(input_file, 'rb') as f:
            original_data = f.read()
        
        if len(original_data) == 0:
            print(f"[-] 错误: 输入文件 '{input_file}' 为空")
            return False
        
        print(f"[+] 读取原始shellcode: {len(original_data)} 字节")
        
        # 生成随机密钥
        key = generate_random_key(16)  # 16字节密钥
        print(f"[+] 生成随机密钥: {key.hex()}")
        
        # 加密数据
        encrypted_data = xor_encrypt(original_data, key)
        print(f"[+] 数据加密完成: {len(encrypted_data)} 字节")
        
        # 保存加密后的文件
        with open(output_file, 'wb') as f:
            f.write(encrypted_data)
        print(f"[+] 加密文件已保存: {output_file}")
        
        # 生成C++头文件格式的密钥
        with open(keyheader_file, 'w') as f:
            f.write("// 自动生成的密钥头文件\n")
            f.write("#pragma once\n\n")
            f.write("// XOR解密密钥\n")
            f.write("constexpr unsigned char SHELLCODE_KEY[] = {\n    ")
            
            key_bytes = [f"0x{b:02X}" for b in key]
            for i, byte_str in enumerate(key_bytes):
                if i > 0 and i % 8 == 0:
                    f.write(",\n    ")
                elif i > 0:
                    f.write(", ")
                f.write(byte_str)
            
            f.write("\n};\n\n")
            f.write(f"constexpr size_t SHELLCODE_KEY_SIZE = {len(key)};\n")
        
        print(f"[+] C++头文件已生成: {keyheader_file}")
        
        return True
        
    except Exception as e:
        print(f"[-] 加密过程中发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("    Shellcode XOR 加密工具")
    print("    用于加密shellcode.bin文件")
    print("=" * 60)
    
    # 默认文件路径
    input_file = "shellcode.bin"
    encrypted_file = "config.dat"
    keyheader_file = "shellcode.h"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        encrypted_file = sys.argv[2]
    if len(sys.argv) > 3:
        keyheader_file = sys.argv[3]
    
    print(f"[*] 输入文件: {input_file}")
    print(f"[*] 输出文件: {encrypted_file}")
    print(f"[*] 密钥头文件: {keyheader_file}")
    print()
    
    # 执行加密
    if save_encrypted_file(input_file, encrypted_file, keyheader_file):
        print()
        print("[+] 加密完成! 使用说明:")
        print(f"    1. 将 {encrypted_file} 重命名为 shellcode.bin")
        print(f"    2. 将生成的 {keyheader_file} 包含到项目中")
        print("    3. 重新编译项目")
    else:
        print("[-] 加密失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
