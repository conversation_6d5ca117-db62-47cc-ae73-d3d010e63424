﻿  Assembling include\syscalls_masm.asm...
  Dllmain.cpp
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(75,1): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2552,1):
  参见“STATUS_TIMEOUT”的前一个定义
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(76,1): warning C4005: “STATUS_PENDING”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2553,1):
  参见“STATUS_PENDING”的前一个定义
  
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(689,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(637,40): warning C4101: “e”: 未引用的局部变量
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(752,30): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(837,26): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(927,38): warning C4101: “e”: 未引用的局部变量
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1016,38): warning C4101: “e”: 未引用的局部变量
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1178,79): warning C4267: “参数”: 从“size_t”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1167,42): warning C4101: “e”: 未引用的局部变量
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1226,42): warning C4101: “e”: 未引用的局部变量
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1274,42): warning C4101: “e”: 未引用的局部变量
C:\Userfile\ccode\BWcode\BWcode\Dllmain.cpp(1351,38): warning C4101: “e”: 未引用的局部变量
  obfuscation.cpp
  syscalls.cpp
Dllmain.obj : warning LNK4197: 多次指定导出“RmEndSession”；使用第一个规范
Dllmain.obj : warning LNK4197: 多次指定导出“RmGetList”；使用第一个规范
Dllmain.obj : warning LNK4197: 多次指定导出“RmStartSession”；使用第一个规范
Dllmain.obj : warning LNK4197: 多次指定导出“RmRegisterResources”；使用第一个规范
    正在创建库 C:\Userfile\ccode\BWcode\x64\Release\BWcode.lib 和对象 C:\Userfile\ccode\BWcode\x64\Release\BWcode.exp
  正在生成代码
  Previous IPDB not found, fall back to full compilation.
  All 1885 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
  BWcode.vcxproj -> C:\Userfile\ccode\BWcode\x64\Release\BWcode.dll
